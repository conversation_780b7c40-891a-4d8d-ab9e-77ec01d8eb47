# Claudia Windows Setup Guide

This guide helps you set up and run Claudia on Windows 10/11.

## 🚀 Quick Start

### Option 1: Use the Batch Scripts (Recommended)

1. **Build the application:**
   ```cmd
   build-claudia.bat
   ```

2. **Run the application:**
   ```cmd
   start-claudia.bat
   ```

### Option 2: Manual Commands

1. **Install dependencies:**
   ```cmd
   bun install
   ```

2. **Build for debug:**
   ```cmd
   bunx tauri build --debug
   ```

3. **Run the application:**
   ```cmd
   src-tauri\target\debug\claudia.exe
   ```

## 📁 Available Scripts

### `start-claudia.bat`
- **Purpose:** Launch the built Claudia application
- **Features:**
  - Automatically detects release or debug builds
  - Checks for Claude Code CLI availability
  - Provides helpful error messages and tips
  - Works with both release and debug builds

### `start-claudia-dev.bat`
- **Purpose:** Start Claudia in development mode
- **Features:**
  - Hot reload enabled
  - Automatic dependency installation
  - Development server with live updates
  - Useful for development and testing

### `build-claudia.bat`
- **Purpose:** Build the Claudia application
- **Features:**
  - Interactive build type selection (debug/release)
  - Automatic dependency installation
  - Error checking and validation
  - Clear success/failure feedback

## 🔧 Prerequisites

Make sure you have the following installed:

1. **Bun** (JavaScript runtime)
   - Download: https://bun.sh/
   - Verify: `bun --version`

2. **Rust** (Programming language)
   - Download: https://rustup.rs/
   - Verify: `cargo --version`

3. **Claude Code CLI** (Required for Claudia to function)
   - Install via npm: `npm install -g @anthropic-ai/claude-code`
   - Verify: `claude --version`

4. **Git** (Version control)
   - Download: https://git-scm.com/
   - Verify: `git --version`

## 🐛 Troubleshooting

### "Claudia executable not found"
- Run `build-claudia.bat` to build the application first
- Make sure the build completed successfully

### "Claude Code CLI not found"
- Install Claude Code CLI: `npm install -g @anthropic-ai/claude-code`
- Restart your terminal/command prompt
- Verify installation: `claude --version`

### "Bun is not installed"
- Download and install Bun from https://bun.sh/
- Restart your terminal/command prompt
- Verify installation: `bun --version`

### "Rust/Cargo is not installed"
- Download and install Rust from https://rustup.rs/
- Restart your terminal/command prompt
- Verify installation: `cargo --version`

### Build Errors
- Make sure all prerequisites are installed
- Try deleting `node_modules` and running `bun install` again
- Check that you have sufficient disk space
- Ensure your antivirus isn't blocking the build process

## 📝 Usage Tips

1. **First Time Setup:**
   - Run `build-claudia.bat` to build the application
   - Choose debug build for faster compilation
   - Use release build for better performance

2. **Development:**
   - Use `start-claudia-dev.bat` for development with hot reload
   - Use `start-claudia.bat` for running the built application

3. **Performance:**
   - Release builds are smaller and faster
   - Debug builds are larger but include debugging information
   - The launcher automatically prefers release builds

## 🎯 Next Steps

After successfully launching Claudia:

1. **Configure Claude Code CLI** if not already done
2. **Import existing projects** or create new ones
3. **Explore the features:**
   - Project and session management
   - Custom AI agents
   - Usage analytics dashboard
   - MCP server management

## 📞 Support

If you encounter issues:

1. Check the error messages in the console
2. Verify all prerequisites are installed
3. Try rebuilding the application
4. Check the project's GitHub repository for known issues

---

**Happy coding with Claudia! 🎉**
