{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 15854849895398670920], [1582828171158827377, "build_script_build", false, 4297971975822802124], [5986029879202738730, "log", false, 4301258038039594785], [9451456094439810778, "regex", false, 1065499532444342601], [9538054652646069845, "tokio", false, 6738638072610150013], [9689903380558560274, "serde", false, 13629682011884409770], [10755362358622467486, "tauri", false, 2661391978245740333], [10806645703491011684, "thiserror", false, 1664844822900897097], [11337703028400419576, "os_pipe", false, 8898084307753089884], [14564311161534545801, "encoding_rs", false, 6720089881567058091], [15367738274754116744, "serde_json", false, 7658123096800002146], [16192041687293812804, "open", false, 5746445474905372405]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-4f317db90ebec38c\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}