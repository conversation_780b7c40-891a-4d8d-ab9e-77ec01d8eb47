{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 17604022209686676412, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[192435385979317305, "rusqlite", false, 17476645404875321469], [246920333930397414, "tauri_plugin_global_shortcut", false, 8181377773505177225], [895066815307484583, "which", false, 11112375349508961633], [1441306149310335789, "tempfile", false, 12641184687534512378], [1582828171158827377, "tauri_plugin_shell", false, 15331309226062678747], [2706460456408817945, "futures", false, 17832521421841198883], [2924422107542798392, "libc", false, 5985584485345838428], [3834743577069889284, "tauri_plugin_dialog", false, 12357463009000321668], [4052408954973158025, "zstd", false, 5244877963170365792], [5986029879202738730, "log", false, 4301258038039594785], [6898646762435821041, "env_logger", false, 13030513820849826565], [7849236192756901113, "tauri_plugin_notification", false, 10418499556133027743], [8218178811151724123, "reqwest", false, 18375268905678187620], [8256202458064874477, "dirs", false, 10231695043306602358], [8319709847752024821, "uuid", false, 1969913188526917788], [9451456094439810778, "regex", false, 1065499532444342601], [9538054652646069845, "tokio", false, 6738638072610150013], [9614479274285663593, "serde_yaml", false, 5145062033697291593], [9689903380558560274, "serde", false, 13629682011884409770], [9805105373369294601, "claudia_lib", false, 9404066076721763095], [9805105373369294601, "build_script_build", false, 16673408003138441691], [9857275760291862238, "sha2", false, 18080213050027635093], [9897246384292347999, "chrono", false, 671361304623747225], [10755362358622467486, "tauri", false, 2661391978245740333], [11946729385090170470, "async_trait", false, 14859593242052595464], [13077212702700853852, "base64", false, 14352512734080678932], [13625485746686963219, "anyhow", false, 17939884661928602801], [13890802266741835355, "tauri_plugin_fs", false, 1973890229439047611], [13919194856117907555, "tauri_plugin_clipboard_manager", false, 3156221278944117504], [15367738274754116744, "serde_json", false, 7658123096800002146], [15441187897486245138, "tauri_plugin_http", false, 10799529424576315483], [15622660310229662834, "walkdir", false, 16898994559113591605], [17155886227862585100, "glob", false, 4601722822262977862], [17962022290347926134, "tauri_plugin_process", false, 1571847855044929565], [18440762029541581206, "tauri_plugin_updater", false, 4607565963599568803]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\claudia-d1df46e427e7ee26\\dep-bin-claudia", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}