@echo off
REM Claudia Development Mode Launcher
REM This script starts Claudia in development mode with hot reload

echo ========================================
echo    Claudia Development Mode Launcher
echo ========================================
echo.

REM Change to the directory where this script is located
cd /d "%~dp0"

REM Check if Node.js/Bun is available
where bun >nul 2>&1
if errorlevel 1 (
    echo Error: Bun is not installed or not in PATH!
    echo Please install Bun first: https://bun.sh/
    echo.
    pause
    exit /b 1
)

REM Check if Rust/Cargo is available
where cargo >nul 2>&1
if errorlevel 1 (
    echo Error: Rust/Cargo is not installed or not in PATH!
    echo Please install Rust first: https://rustup.rs/
    echo.
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    bun install
    if errorlevel 1 (
        echo Error: Failed to install dependencies!
        pause
        exit /b 1
    )
)

echo Starting Claudia in development mode...
echo This will start the application with hot reload enabled.
echo Press Ctrl+C to stop the development server.
echo.

REM Start in development mode
bunx tauri dev

echo.
echo Development server stopped.
pause
