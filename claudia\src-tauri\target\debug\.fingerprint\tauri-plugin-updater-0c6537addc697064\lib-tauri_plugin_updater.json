{"rustc": 3062648155896360161, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 15657897354478470176, "path": 6356521903398711350, "deps": [[40386456601120721, "percent_encoding", false, 15842239878754744876], [1441306149310335789, "tempfile", false, 12641184687534512378], [3150220818285335163, "url", false, 10594202264036559037], [4899080583175475170, "semver", false, 4523284147179220673], [5986029879202738730, "log", false, 4301258038039594785], [6873154789535483674, "zip", false, 15833784519181503028], [7263319592666514104, "windows_sys", false, 7654545091316702246], [8218178811151724123, "reqwest", false, 18375268905678187620], [9010263965687315507, "http", false, 11039511902789686861], [9332307739160395223, "minisign_verify", false, 11859027127194251032], [9538054652646069845, "tokio", false, 6738638072610150013], [9689903380558560274, "serde", false, 13629682011884409770], [10629569228670356391, "futures_util", false, 18206589142207579641], [10755362358622467486, "tauri", false, 2661391978245740333], [10806645703491011684, "thiserror", false, 1664844822900897097], [12409575957772518135, "time", false, 5112570382594833540], [13077212702700853852, "base64", false, 14352512734080678932], [15367738274754116744, "serde_json", false, 7658123096800002146], [17146114186171651583, "infer", false, 11831510616302393155], [18440762029541581206, "build_script_build", false, 7896789743412081381]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-0c6537addc697064\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}