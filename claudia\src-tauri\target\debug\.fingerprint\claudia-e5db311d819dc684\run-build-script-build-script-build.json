{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 3288833082118465863], [10755362358622467486, "build_script_build", false, 6222596511803184807], [13919194856117907555, "build_script_build", false, 1594393391951199742], [3834743577069889284, "build_script_build", false, 8183997438130816781], [13890802266741835355, "build_script_build", false, 9379851932027954010], [246920333930397414, "build_script_build", false, 4488012619549788882], [15441187897486245138, "build_script_build", false, 8534067926096195839], [7849236192756901113, "build_script_build", false, 9918132835909277688], [17962022290347926134, "build_script_build", false, 9810076805163598350], [1582828171158827377, "build_script_build", false, 4297971975822802124], [18440762029541581206, "build_script_build", false, 7896789743412081381]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-e5db311d819dc684\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}