@echo off
echo Testing Claudia launcher...
cd /d "%~dp0"

echo Current directory: %CD%

echo Checking for release build...
if exist "src-tauri\target\release\claudia.exe" (
    echo Found release build!
    set EXECUTABLE_PATH=src-tauri\target\release\claudia.exe
    set BUILD_TYPE=Release
    goto :found
)

echo Checking for debug build...
if exist "src-tauri\target\debug\claudia.exe" (
    echo Found debug build!
    set EXECUTABLE_PATH=src-tauri\target\debug\claudia.exe
    set BUILD_TYPE=Debug
    goto :found
)

echo No executable found!
pause
exit /b 1

:found
echo Using: %EXECUTABLE_PATH% (%BUILD_TYPE%)
echo Starting application...
start "" "%EXECUTABLE_PATH%"
echo Application started!
pause
