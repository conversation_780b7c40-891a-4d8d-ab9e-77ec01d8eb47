@echo off
REM Claudia Desktop Application Launcher
REM This script starts the Claudia application for managing Claude Code projects

echo ========================================
echo     Claudia Desktop Application
echo ========================================
echo.

REM Change to the directory where this script is located
cd /d "%~dp0"

REM Check for executable in order of preference: release, then debug
set EXECUTABLE_PATH=
set BUILD_TYPE=

if exist "src-tauri\target\release\claudia.exe" (
    set EXECUTABLE_PATH=src-tauri\target\release\claudia.exe
    set BUILD_TYPE=Release
    goto :found
)

if exist "src-tauri\target\debug\claudia.exe" (
    set EXECUTABLE_PATH=src-tauri\target\debug\claudia.exe
    set BUILD_TYPE=Debug
    goto :found
)

echo ❌ Error: Claudia executable not found!
echo.
echo Please build the application first using one of these methods:
echo   1. Run: build-claudia.bat
echo   2. Run: bunx tauri build --debug  (for debug build)
echo   3. Run: bunx tauri build          (for release build)
echo.
pause
exit /b 1

:found

REM Check if Claude Code CLI is available
where claude >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Warning: Claude Code CLI not found in PATH!
    echo Claudia may not function properly without Claude Code CLI.
    echo Please install Claude Code CLI first.
    echo.
)

REM Start Claudia
echo 🚀 Launching Claudia Desktop Application (%BUILD_TYPE% build)...
echo.
start "" "%EXECUTABLE_PATH%"

REM Wait a moment for the application to start
timeout /t 3 /nobreak >nul

echo ✅ Claudia has been launched successfully!
echo.
echo 💡 Tips:
echo   - The application should open in a new window
echo   - If you encounter issues, check the console output
echo   - Use start-claudia-dev.bat for development mode
echo.
echo You can now close this window.
pause