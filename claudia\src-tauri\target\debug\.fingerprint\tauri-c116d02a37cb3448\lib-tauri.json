{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 15842239878754744876], [442785307232013896, "tauri_runtime", false, 9144771640862000363], [1200537532907108615, "url<PERSON><PERSON>n", false, 14347403427884194738], [3150220818285335163, "url", false, 10594202264036559037], [4143744114649553716, "raw_window_handle", false, 2523135429908484991], [4341921533227644514, "muda", false, 3798363927261034203], [4919829919303820331, "serialize_to_javascript", false, 5260286798833699663], [5986029879202738730, "log", false, 4301258038039594785], [7752760652095876438, "tauri_runtime_wry", false, 1214380812197344329], [8351317599104215083, "tray_icon", false, 16831539494076243779], [8539587424388551196, "webview2_com", false, 5699180442623757141], [8866577183823226611, "http_range", false, 5893001753669596449], [9010263965687315507, "http", false, 11039511902789686861], [9228235415475680086, "tauri_macros", false, 14475543276979512846], [9538054652646069845, "tokio", false, 6738638072610150013], [9689903380558560274, "serde", false, 13629682011884409770], [9920160576179037441, "getrandom", false, 751064634263608030], [10229185211513642314, "mime", false, 16472311026408746956], [10629569228670356391, "futures_util", false, 18206589142207579641], [10755362358622467486, "build_script_build", false, 6222596511803184807], [10806645703491011684, "thiserror", false, 1664844822900897097], [11050281405049894993, "tauri_utils", false, 11646392722781894552], [11989259058781683633, "dunce", false, 10333777902361752181], [12565293087094287914, "window_vibrancy", false, 8403239314792290870], [12986574360607194341, "serde_repr", false, 12722511013507459832], [13028763805764736075, "image", false, 14510226756575545301], [13077543566650298139, "heck", false, 11354297438612443554], [13116089016666501665, "windows", false, 4200490090171702092], [13625485746686963219, "anyhow", false, 17939884661928602801], [15367738274754116744, "serde_json", false, 7658123096800002146], [16928111194414003569, "dirs", false, 12297036453650602642], [17155886227862585100, "glob", false, 4601722822262977862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-c116d02a37cb3448\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}