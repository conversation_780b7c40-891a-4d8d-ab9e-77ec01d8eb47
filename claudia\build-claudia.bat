@echo off
REM Claudia Build Script
REM This script builds the Claudia application for production or debug

echo ========================================
echo        Claudia Build Script
echo ========================================
echo.

REM Change to the directory where this script is located
cd /d "%~dp0"

REM Check if Bun is available
where bun >nul 2>&1
if errorlevel 1 (
    echo Error: Bun is not installed or not in PATH!
    echo Please install Bun first: https://bun.sh/
    echo.
    pause
    exit /b 1
)

REM Check if Rust/Cargo is available
where cargo >nul 2>&1
if errorlevel 1 (
    echo Error: Rust/Cargo is not installed or not in PATH!
    echo Please install Rust first: https://rustup.rs/
    echo.
    pause
    exit /b 1
)

REM Ask user for build type
echo Select build type:
echo 1. Debug build (faster, larger file, with debug info)
echo 2. Release build (slower, smaller file, optimized)
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    set BUILD_TYPE=debug
    set BUILD_CMD=bunx tauri build --debug
    echo Building in DEBUG mode...
) else if "%choice%"=="2" (
    set BUILD_TYPE=release
    set BUILD_CMD=bunx tauri build
    echo Building in RELEASE mode...
) else (
    echo Invalid choice. Defaulting to debug build.
    set BUILD_TYPE=debug
    set BUILD_CMD=bunx tauri build --debug
)

echo.
echo Installing/updating dependencies...
bun install
if errorlevel 1 (
    echo Error: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo Starting build process...
echo This may take several minutes...
echo.

%BUILD_CMD%

if errorlevel 1 (
    echo.
    echo ❌ Build failed!
    echo Check the error messages above for details.
    pause
    exit /b 1
) else (
    echo.
    echo ✅ Build completed successfully!
    if "%BUILD_TYPE%"=="debug" (
        echo Executable location: src-tauri\target\debug\claudia.exe
    ) else (
        echo Executable location: src-tauri\target\release\claudia.exe
    )
    echo.
    echo You can now run start-claudia.bat to launch the application.
)

echo.
pause
