{"rustc": 3062648155896360161, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 2927926812607995470, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 14347403427884194738], [3150220818285335163, "url", false, 10594202264036559037], [8218178811151724123, "reqwest", false, 18375268905678187620], [8298091525883606470, "cookie_store", false, 9900373130118588330], [9010263965687315507, "http", false, 11039511902789686861], [9451456094439810778, "regex", false, 1065499532444342601], [9538054652646069845, "tokio", false, 6738638072610150013], [9689903380558560274, "serde", false, 13629682011884409770], [10755362358622467486, "tauri", false, 2661391978245740333], [10806645703491011684, "thiserror", false, 1664844822900897097], [13890802266741835355, "tauri_plugin_fs", false, 1973890229439047611], [15367738274754116744, "serde_json", false, 7658123096800002146], [15441187897486245138, "build_script_build", false, 8534067926096195839], [16066129441945555748, "bytes", false, 2645974509654522595], [17047088963840213854, "data_url", false, 16789316343909981034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-5feff27e255ce8fe\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}